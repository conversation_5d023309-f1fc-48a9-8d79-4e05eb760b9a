import type { <PERSON>ada<PERSON> } from "next";
import { Poppins, Inter } from "next/font/google";
import "./globals.css";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "800", "900"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "<PERSON> Khan - 3D Modeler & Web Developer",
  description: "Bringing your digital ideas to life. Expert in 3D modeling, web development, and interactive design.",
  keywords: ["3D modeling", "web development", "React", "Next.js", "Three.js", "interactive design"],
  authors: [{ name: "<PERSON>" }],
  creator: "<PERSON>",
  openGraph: {
    title: "<PERSON> - 3D Modeler & Web Developer",
    description: "Bringing your digital ideas to life. Expert in 3D modeling, web development, and interactive design.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${poppins.variable} ${inter.variable} antialiased min-h-screen font-sans bg-background text-foreground`}
      >
        {children}
      </body>
    </html>
  );
}
