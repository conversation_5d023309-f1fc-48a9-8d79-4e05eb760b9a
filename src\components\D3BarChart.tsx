'use client';

import { useEffect, useRef, useState, useMemo } from 'react';
import * as d3 from 'd3';

interface BrandData {
  name: string;
  value: number;
  date: Date;
  color: string;
  rank?: number;
}

interface KeyFrame {
  date: Date;
  data: BrandData[];
}

const D3BarChart = () => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [currentDateIndex, setCurrentDateIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Enhanced sample data with daily progression
  const baseData = useMemo(() => [
    { name: 'Apple', baseValue: 355000, color: '#007AFF' },
    { name: 'Microsoft', baseValue: 290000, color: '#00BCF2' },
    { name: 'Amazon', baseValue: 254000, color: '#FF9900' },
    { name: 'Google', baseValue: 191000, color: '#4285F4' },
    { name: 'Samsung', baseValue: 87000, color: '#1428A0' },
    { name: 'Tesla', baseValue: 66000, color: '#CC0000' },
    { name: 'Meta', baseValue: 56000, color: '#1877F2' },
    { name: 'NVIDIA', baseValue: 48000, color: '#76B900' },
    { name: 'Netflix', baseValue: 45000, color: '#E50914' },
    { name: 'Adobe', baseValue: 42000, color: '#FF0000' },
    { name: 'IBM', baseValue: 120000, color: '#1F70C1' },
    { name: 'Intel', baseValue: 95000, color: '#0071C5' },
  ], []);

  // Generate keyframes for each day over a period
  const keyframes = useMemo(() => {
    const startDate = new Date(2023, 0, 1); // January 1, 2023
    const endDate = new Date(2023, 11, 31); // December 31, 2023
    const frames: KeyFrame[] = [];

    const totalDays = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    for (let day = 0; day <= totalDays; day++) {
      const currentDate = new Date(startDate.getTime() + day * 24 * 60 * 60 * 1000);
      const dayProgress = day / totalDays;

      const data = baseData.map((brand, index) => {
        // Create realistic growth patterns over the year
        const seasonalFactor = 1 + 0.3 * Math.sin((dayProgress * 2 * Math.PI) + index);
        const trendFactor = 0.8 + 0.4 * dayProgress + 0.1 * Math.sin((dayProgress * 4 * Math.PI) + index);
        const randomWalk = 0.95 + 0.1 * Math.sin((day / 7) + index * 2); // Weekly cycles

        const value = brand.baseValue * seasonalFactor * trendFactor * randomWalk;

        return {
          name: brand.name,
          value: Math.max(1000, value),
          date: currentDate,
          color: brand.color
        };
      }).sort((a, b) => b.value - a.value)
        .slice(0, 10)
        .map((d, i) => ({ ...d, rank: i }));

      frames.push({ date: currentDate, data });
    }

    return frames;
  }, [baseData]);

  // Chart initialization and setup
  useEffect(() => {
    if (!svgRef.current || keyframes.length === 0) return;

    const svg = d3.select(svgRef.current);
    const margin = { top: 60, right: 30, bottom: 40, left: 120 };
    const width = 1000 - margin.left - margin.right;
    const height = 500 - margin.top - margin.bottom;
    const n = 10; // Number of bars to show
    const duration = 250; // Animation duration

    // Clear previous content
    svg.selectAll("*").remove();

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Get current keyframe
    const currentKeyframe = keyframes[currentDateIndex];
    const data = currentKeyframe.data.slice(0, n);

    // Scales
    const x = d3.scaleLinear()
      .domain([0, d3.max(keyframes, d => d3.max(d.data, dd => dd.value)) || 0])
      .range([0, width]);

    const y = d3.scaleBand()
      .domain(d3.range(n).map(String))
      .range([0, height])
      .padding(0.1);

    // Date display
    const dateText = svg.append("text")
      .attr("class", "date-display")
      .attr("x", width / 2 + margin.left)
      .attr("y", 40)
      .style("text-anchor", "middle")
      .style("font-size", "32px")
      .style("font-weight", "bold")
      .style("fill", "#6366f1")
      .style("opacity", 0.8)
      .text(d3.timeFormat("%B %d, %Y")(currentKeyframe.date));

    // X-axis
    const xAxis = g.append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(x)
        .tickFormat(d => `$${(d as number / 1000).toFixed(0)}B`)
        .ticks(5))
      .selectAll("text")
      .style("fill", "#ffffff");

    // Create bar groups
    const bar = g.append("g")
      .attr("fill-opacity", 0.8)
      .selectAll("rect");

    // Create label groups
    const label = g.append("g")
      .style("font", "bold 14px var(--sans-serif)")
      .style("font-variant-numeric", "tabular-nums")
      .attr("text-anchor", "end")
      .selectAll("text");

    // Create value groups
    const valueLabel = g.append("g")
      .style("font", "12px var(--sans-serif)")
      .style("font-variant-numeric", "tabular-nums")
      .selectAll("text");

    // Store references for animation
    svg.datum({
      bar, label, valueLabel, dateText, x, y, width, height, margin, n, duration, g
    });

  }, [keyframes, currentDateIndex]);

  // Animation update effect
  useEffect(() => {
    if (!svgRef.current || keyframes.length === 0) return;

    const svg = d3.select(svgRef.current);
    const svgData = svg.datum() as any;

    if (!svgData) return;

    const { bar, dateText, x, y, n, duration } = svgData;
    const currentKeyframe = keyframes[currentDateIndex];
    const data = currentKeyframe.data.slice(0, n);

    // Update date display
    dateText
      .transition()
      .duration(duration)
      .text(d3.timeFormat("%B %d, %Y")(currentKeyframe.date));

    // Update bars with proper enter/exit/update pattern
    const bars = bar
      .data(data, (d: BrandData) => d.name)
      .join(
        (enter: any) => enter.append("rect")
          .attr("fill", (d: BrandData) => d.color)
          .attr("height", y.bandwidth())
          .attr("x", x(0))
          .attr("y", (d: BrandData, i: number) => y(i))
          .attr("width", (d: BrandData) => x(d.value) - x(0)),
        (update: any) => update,
        (exit: any) => exit.transition().duration(duration).remove()
          .attr("y", (d: BrandData) => y(n))
          .attr("width", (d: BrandData) => x(d.value) - x(0))
      );

    bars.transition()
      .duration(duration)
      .ease(d3.easeCubicInOut)
      .attr("y", (d: BrandData, i: number) => y(i))
      .attr("width", (d: BrandData) => x(d.value) - x(0));

  }, [keyframes, currentDateIndex]);

  const handlePlay = () => {
    if (isPlaying) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsPlaying(false);
    } else {
      setIsPlaying(true);
      intervalRef.current = setInterval(() => {
        setCurrentDateIndex(prev => {
          if (prev >= keyframes.length - 1) {
            setIsPlaying(false);
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
            return 0;
          }
          return prev + 1;
        });
      }, 100); // Fast updates for smooth racing effect
    }
  };

  const handleDateChange = (index: number) => {
    setCurrentDateIndex(index);
    if (isPlaying) {
      setIsPlaying(false);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const currentDate = keyframes[currentDateIndex]?.date;

  return (
    <section className="section-padding bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Global Brands{' '}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Race 2023
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Watch the daily race of the world&apos;s most valuable brands throughout 2023
          </p>
        </div>

        {/* Chart Container */}
        <div className="bg-background/50 backdrop-blur-sm rounded-2xl border border-border p-8 w-auto">
          <div className="flex flex-col items-center">
            {/* SVG Chart */}
            <svg
              ref={svgRef}
              width="1000"
              height="500"
              className="mb-8"
            />

            {/* Controls */}
            <div className="flex flex-col items-center space-y-4 w-full max-w-2xl">
              {/* Play/Pause Button */}
              <button
                onClick={handlePlay}
                className="px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105"
              >
                {isPlaying ? 'Pause' : 'Play'} Animation
              </button>

              {/* Date Slider */}
              <div className="w-full">
                <input
                  type="range"
                  min="0"
                  max={keyframes.length - 1}
                  step="1"
                  value={currentDateIndex}
                  onChange={(e) => handleDateChange(parseInt(e.target.value))}
                  className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-sm text-muted-foreground mt-2">
                  <span>Jan 1, 2023</span>
                  <span className="font-bold text-primary">
                    {currentDate ? d3.timeFormat("%b %d, %Y")(currentDate) : 'Loading...'}
                  </span>
                  <span>Dec 31, 2023</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #6366f1;
          cursor: pointer;
          box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #6366f1;
          cursor: pointer;
          border: none;
          box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
        }
      `}</style>
    </section>
  );
};

export default D3BarChart;
