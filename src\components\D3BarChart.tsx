'use client';

import { useEffect, useRef, useState, useMemo } from 'react';
import * as d3 from 'd3';

interface BrandData {
  name: string;
  value: number;
  date: Date;
  color: string;
  rank?: number;
}

interface KeyFrame {
  date: Date;
  data: BrandData[];
}

const D3BarChart = () => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [currentDateIndex, setCurrentDateIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Enhanced sample data with realistic growth patterns from 2000-2025
  const baseData = useMemo(() => [
    { name: 'Apple', baseValue: 355000, color: '#007AFF', growthPattern: [0.01, 0.05, 0.15, 0.4, 0.7, 1.0] },
    { name: 'Microsoft', baseValue: 290000, color: '#00BCF2', growthPattern: [0.3, 0.4, 0.5, 0.7, 0.9, 0.95] },
    { name: 'Amazon', baseValue: 254000, color: '#FF9900', growthPattern: [0.02, 0.1, 0.3, 0.6, 0.8, 0.9] },
    { name: 'Google', baseValue: 191000, color: '#4285F4', growthPattern: [0.0, 0.05, 0.2, 0.5, 0.8, 0.85] },
    { name: 'Samsung', baseValue: 87000, color: '#1428A0', growthPattern: [0.4, 0.5, 0.6, 0.7, 0.75, 0.8] },
    { name: 'Tesla', baseValue: 66000, color: '#CC0000', growthPattern: [0.0, 0.0, 0.01, 0.1, 0.6, 0.8] },
    { name: 'Meta', baseValue: 56000, color: '#1877F2', growthPattern: [0.0, 0.0, 0.05, 0.3, 0.7, 0.65] },
    { name: 'NVIDIA', baseValue: 48000, color: '#76B900', growthPattern: [0.05, 0.1, 0.15, 0.3, 0.6, 0.9] },
    { name: 'Netflix', baseValue: 45000, color: '#E50914', growthPattern: [0.0, 0.02, 0.1, 0.4, 0.6, 0.7] },
    { name: 'Adobe', baseValue: 42000, color: '#FF0000', growthPattern: [0.1, 0.2, 0.3, 0.5, 0.7, 0.8] },
    { name: 'IBM', baseValue: 120000, color: '#1F70C1', growthPattern: [0.8, 0.7, 0.6, 0.4, 0.3, 0.2] },
    { name: 'Intel', baseValue: 95000, color: '#0071C5', growthPattern: [0.6, 0.7, 0.8, 0.7, 0.5, 0.4] },
    { name: 'Oracle', baseValue: 85000, color: '#F80000', growthPattern: [0.5, 0.6, 0.65, 0.6, 0.55, 0.5] },
    { name: 'Cisco', baseValue: 75000, color: '#1BA0D7', growthPattern: [0.7, 0.8, 0.75, 0.6, 0.4, 0.3] },
    { name: 'Sony', baseValue: 70000, color: '#000000', growthPattern: [0.6, 0.65, 0.6, 0.55, 0.5, 0.45] },
  ], []);

  // Generate keyframes for each week from 2000 to 2025
  const keyframes = useMemo(() => {
    const startDate = new Date(2000, 0, 1); // January 1, 2000
    const endDate = new Date(2025, 11, 31); // December 31, 2025
    const frames: KeyFrame[] = [];

    const totalWeeks = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7));

    for (let week = 0; week <= totalWeeks; week++) {
      const currentDate = new Date(startDate.getTime() + week * 7 * 24 * 60 * 60 * 1000);
      const yearProgress = Math.max(0, Math.min(1, (currentDate.getFullYear() - 2000) / 25));

      const data = baseData.map((brand, index) => {
        // Map progress to growth pattern segments (6 segments over 25 years)
        const segmentIndex = Math.floor(yearProgress * 5); // 0-4 index
        const segmentProgress = (yearProgress * 5) % 1;

        const currentGrowth = brand.growthPattern[segmentIndex] || 0;
        const nextGrowth = brand.growthPattern[segmentIndex + 1] || currentGrowth;
        const interpolatedGrowth = currentGrowth + (nextGrowth - currentGrowth) * segmentProgress;

        // Add cyclical variations for more realistic movement
        const cyclicalFactor = 1 + 0.1 * Math.sin((week / 52) * 2 * Math.PI + index); // Annual cycles
        const trendFactor = 1 + 0.05 * Math.sin((week / 26) * 2 * Math.PI + index * 2); // Semi-annual trends

        const value = brand.baseValue * interpolatedGrowth * cyclicalFactor * trendFactor;

        return {
          name: brand.name,
          value: Math.max(1000, value),
          date: currentDate,
          color: brand.color
        };
      }).sort((a, b) => b.value - a.value)
        .slice(0, 10)
        .map((d, i) => ({ ...d, rank: i }));

      frames.push({ date: currentDate, data });
    }

    return frames;
  }, [baseData]);

  // Chart initialization and setup
  useEffect(() => {
    if (!svgRef.current || keyframes.length === 0) return;

    const svg = d3.select(svgRef.current);
    const margin = { top: 60, right: 30, bottom: 40, left: 120 };
    const width = 1000 - margin.left - margin.right;
    const height = 500 - margin.top - margin.bottom;
    const n = 10; // Number of bars to show
    const duration = 250; // Animation duration

    // Clear previous content
    svg.selectAll("*").remove();

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Get current keyframe
    const currentKeyframe = keyframes[currentDateIndex];
    const data = currentKeyframe.data.slice(0, n);

    // Scales
    const x = d3.scaleLinear()
      .domain([0, d3.max(keyframes, d => d3.max(d.data, dd => dd.value)) || 0])
      .range([0, width]);

    const y = d3.scaleBand()
      .domain(d3.range(n).map(String))
      .range([0, height])
      .padding(0.1);

    // Date display
    const dateText = svg.append("text")
      .attr("class", "date-display")
      .attr("x", width / 2 + margin.left)
      .attr("y", 40)
      .style("text-anchor", "middle")
      .style("font-size", "32px")
      .style("font-weight", "bold")
      .style("fill", "#6366f1")
      .style("opacity", 0.8)
      .text(d3.timeFormat("%Y")(currentKeyframe.date));

    // X-axis
    g.append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(x)
        .tickFormat(d => `$${(d as number / 1000).toFixed(0)}B`)
        .ticks(5))
      .selectAll("text")
      .style("fill", "#ffffff");

    // Initial bars - each brand gets its own bar
    g.selectAll(".bar")
      .data(data, (d: any) => d.name)
      .enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", (_d: any, i: number) => y(i.toString()) || 0)
      .attr("width", (d: any) => x(d.value))
      .attr("height", y.bandwidth())
      .attr("fill", (d: any) => d.color)
      .attr("rx", 4)
      .style("opacity", 0.9);

    // Initial labels - each brand gets its own label
    g.selectAll(".label")
      .data(data, (d: any) => d.name)
      .enter()
      .append("text")
      .attr("class", "label")
      .attr("x", -10)
      .attr("y", (_d: any, i: number) => (y(i.toString()) || 0) + y.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("text-anchor", "end")
      .style("font-size", "14px")
      .style("font-weight", "bold")
      .style("fill", "#ffffff")
      .text((d: any) => d.name);

    // Initial values - each brand gets its own value
    g.selectAll(".value")
      .data(data, (d: any) => d.name)
      .enter()
      .append("text")
      .attr("class", "value")
      .attr("x", (d: any) => x(d.value) + 10)
      .attr("y", (_d: any, i: number) => (y(i.toString()) || 0) + y.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("fill", "#ffffff")
      .text((d: any) => `$${(d.value / 1000).toFixed(0)}B`);

    // Store references for animation
    svg.datum({
      dateText, x, y, width, height, margin, n, duration, g
    });

  }, [keyframes, currentDateIndex]);

  // Animation update effect
  useEffect(() => {
    if (!svgRef.current || keyframes.length === 0) return;

    const svg = d3.select(svgRef.current);
    const svgData = svg.datum() as any;

    if (!svgData) return;

    const { dateText, x, y, n, duration, g, height } = svgData;
    const currentKeyframe = keyframes[currentDateIndex];
    const data = currentKeyframe.data.slice(0, n);

    // Update date display
    dateText
      .transition()
      .duration(duration)
      .text(d3.timeFormat("%Y")(currentKeyframe.date));

    // Update bars with proper data binding for each brand
    const bars = g.selectAll(".bar")
      .data(data, (d: any) => d.name);

    // Enter new bars
    bars.enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", height) // Start from bottom
      .attr("width", 0)
      .attr("height", y.bandwidth())
      .attr("fill", (d: any) => d.color)
      .attr("rx", 4)
      .style("opacity", 0.9)
      .merge(bars) // Merge with existing bars
      .transition()
      .duration(duration)
      .ease(d3.easeCubicInOut)
      .attr("y", (_d: any, i: number) => y(i.toString()) || 0)
      .attr("width", (d: any) => x(d.value));

    // Exit old bars
    bars.exit()
      .transition()
      .duration(duration)
      .attr("y", height)
      .attr("width", 0)
      .style("opacity", 0)
      .remove();

    // Update labels with proper data binding for each brand
    const labels = g.selectAll(".label")
      .data(data, (d: any) => d.name);

    // Enter new labels
    labels.enter()
      .append("text")
      .attr("class", "label")
      .attr("x", -10)
      .attr("y", height)
      .attr("dy", "0.35em")
      .style("text-anchor", "end")
      .style("font-size", "14px")
      .style("font-weight", "bold")
      .style("fill", "#ffffff")
      .style("opacity", 0)
      .text((d: any) => d.name)
      .merge(labels) // Merge with existing labels
      .transition()
      .duration(duration)
      .ease(d3.easeCubicInOut)
      .attr("y", (_d: any, i: number) => (y(i.toString()) || 0) + y.bandwidth() / 2)
      .style("opacity", 1);

    // Exit old labels
    labels.exit()
      .transition()
      .duration(duration)
      .attr("y", height)
      .style("opacity", 0)
      .remove();

    // Update values with proper data binding for each brand
    const values = g.selectAll(".value")
      .data(data, (d: any) => d.name);

    // Enter new values
    values.enter()
      .append("text")
      .attr("class", "value")
      .attr("x", 10)
      .attr("y", height)
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("fill", "#ffffff")
      .style("opacity", 0)
      .text((d: any) => `$${(d.value / 1000).toFixed(0)}B`)
      .merge(values) // Merge with existing values
      .transition()
      .duration(duration)
      .ease(d3.easeCubicInOut)
      .attr("x", (d: any) => x(d.value) + 10)
      .attr("y", (_d: any, i: number) => (y(i.toString()) || 0) + y.bandwidth() / 2)
      .style("opacity", 1)
      .tween("text", function(d: any) {
        const element = this as SVGTextElement;
        const interpolator = d3.interpolateNumber(
          +(element.textContent?.replace(/[$B,]/g, '') || '0'),
          d.value / 1000
        );
        return function(t: number) {
          element.textContent = `$${Math.round(interpolator(t))}B`;
        };
      });

    // Exit old values
    values.exit()
      .transition()
      .duration(duration)
      .attr("y", height)
      .style("opacity", 0)
      .remove();

  }, [keyframes, currentDateIndex]);

  const handlePlay = () => {
    if (isPlaying) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsPlaying(false);
    } else {
      setIsPlaying(true);
      intervalRef.current = setInterval(() => {
        setCurrentDateIndex(prev => {
          if (prev >= keyframes.length - 1) {
            setIsPlaying(false);
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
            return 0;
          }
          return prev + 1;
        });
      }, 50); // Fast updates for smooth racing effect over 25 years
    }
  };

  const handleDateChange = (index: number) => {
    setCurrentDateIndex(index);
    if (isPlaying) {
      setIsPlaying(false);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const currentDate = keyframes[currentDateIndex]?.date;

  return (
    <section className="section-padding bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Global Brands{' '}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Evolution 2000-2025
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Watch the weekly evolution of the world&apos;s most valuable brands from 2000 to 2025
          </p>
        </div>

        {/* Chart Container */}
        <div className="bg-background/50 backdrop-blur-sm rounded-2xl border border-border p-8 w-auto">
          <div className="flex flex-col items-center">
            {/* SVG Chart */}
            <svg
              ref={svgRef}
              width="1000"
              height="500"
              className="mb-8"
            />

            {/* Controls */}
            <div className="flex flex-col items-center space-y-4 w-full max-w-2xl">
              {/* Play/Pause Button */}
              <button
                onClick={handlePlay}
                className="px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105"
              >
                {isPlaying ? 'Pause' : 'Play'} Animation
              </button>

              {/* Date Slider */}
              <div className="w-full">
                <input
                  type="range"
                  min="0"
                  max={keyframes.length - 1}
                  step="1"
                  value={currentDateIndex}
                  onChange={(e) => handleDateChange(parseInt(e.target.value))}
                  className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-sm text-muted-foreground mt-2">
                  <span>2000</span>
                  <span className="font-bold text-primary">
                    {currentDate ? d3.timeFormat("%Y")(currentDate) : 'Loading...'}
                  </span>
                  <span>2025</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #6366f1;
          cursor: pointer;
          box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #6366f1;
          cursor: pointer;
          border: none;
          box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
        }
      `}</style>
    </section>
  );
};

export default D3BarChart;
