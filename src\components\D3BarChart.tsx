'use client';

import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import * as d3 from 'd3';

interface BrandData {
  name: string;
  value: number;
  year: number;
  color: string;
  rank?: number;
}

const D3BarChart = () => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [currentYear, setCurrentYear] = useState(1900);
  const [isPlaying, setIsPlaying] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Enhanced sample data with more realistic progression
  const baseData = useMemo(() => [
    { name: 'Apple', baseValue: 355000, color: '#007AFF', growthPattern: [0.1, 0.3, 0.6, 0.8, 1.0] },
    { name: 'Microsoft', baseValue: 290000, color: '#00BCF2', growthPattern: [0.2, 0.4, 0.7, 0.9, 0.95] },
    { name: 'Amazon', baseValue: 254000, color: '#FF9900', growthPattern: [0.05, 0.2, 0.5, 0.8, 0.9] },
    { name: 'Google', baseValue: 191000, color: '#4285F4', growthPattern: [0.0, 0.1, 0.4, 0.7, 0.85] },
    { name: 'Samsung', baseValue: 87000, color: '#1428A0', growthPattern: [0.3, 0.5, 0.6, 0.7, 0.75] },
    { name: 'Tesla', baseValue: 66000, color: '#CC0000', growthPattern: [0.0, 0.0, 0.1, 0.6, 0.8] },
    { name: 'Meta', baseValue: 56000, color: '#1877F2', growthPattern: [0.0, 0.0, 0.2, 0.7, 0.65] },
    { name: 'NVIDIA', baseValue: 48000, color: '#76B900', growthPattern: [0.0, 0.1, 0.2, 0.5, 0.9] },
    { name: 'Netflix', baseValue: 45000, color: '#E50914', growthPattern: [0.0, 0.1, 0.3, 0.6, 0.7] },
    { name: 'Adobe', baseValue: 42000, color: '#FF0000', growthPattern: [0.1, 0.2, 0.4, 0.6, 0.8] },
    { name: 'IBM', baseValue: 120000, color: '#1F70C1', growthPattern: [0.8, 0.7, 0.5, 0.3, 0.2] },
    { name: 'Intel', baseValue: 95000, color: '#0071C5', growthPattern: [0.6, 0.7, 0.6, 0.5, 0.4] },
  ], []);

  const generateYearData = useCallback((year: number): BrandData[] => {
    const yearProgress = Math.max(0, Math.min(1, (year - 1900) / 125)); // 1900-2025 range
    const progressIndex = Math.floor(yearProgress * 4); // 0-4 index for growth pattern
    const progressFraction = (yearProgress * 4) % 1;

    return baseData.map(brand => {
      const currentGrowth = brand.growthPattern[progressIndex] || 0;
      const nextGrowth = brand.growthPattern[progressIndex + 1] || currentGrowth;
      const interpolatedGrowth = currentGrowth + (nextGrowth - currentGrowth) * progressFraction;

      // Add some randomness for more dynamic movement
      const randomFactor = 0.9 + Math.random() * 0.2;
      const value = brand.baseValue * interpolatedGrowth * randomFactor;

      return {
        name: brand.name,
        value: Math.max(1000, value), // Minimum value to avoid zero
        year,
        color: brand.color
      };
    }).sort((a, b) => b.value - a.value)
      .slice(0, 10)
      .map((d, i) => ({ ...d, rank: i }));
  }, [baseData]);

  // Chart setup and initialization
  useEffect(() => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    const margin = { top: 60, right: 30, bottom: 40, left: 120 };
    const width = 1000 - margin.left - margin.right;
    const height = 500 - margin.top - margin.bottom;

    // Clear and setup SVG
    svg.selectAll("*").remove();

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Add year display
    const yearText = svg.append("text")
      .attr("class", "year-display")
      .attr("x", width / 2 + margin.left)
      .attr("y", 40)
      .style("text-anchor", "middle")
      .style("font-size", "32px")
      .style("font-weight", "bold")
      .style("fill", "#6366f1")
      .style("opacity", 0.8)
      .text(currentYear);

    // Add axis container
    const xAxisGroup = g.append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0,${height})`);

    // Store references for updates
    svg.datum({ g, xAxisGroup, yearText, width, height, margin });

    // Initial render
    const data = generateYearData(currentYear);
    const maxValue = d3.max(data, d => d.value) || 0;
    const x = d3.scaleLinear()
      .domain([0, maxValue])
      .range([0, width]);

    const y = d3.scaleBand()
      .domain(data.map((_, i) => i.toString()))
      .range([0, height])
      .padding(0.1);

    // Initial x-axis
    xAxisGroup
      .call(d3.axisBottom(x)
        .tickFormat(d => `$${(d as number / 1000).toFixed(0)}B`)
        .ticks(5))
      .selectAll("text")
      .style("fill", "#ffffff");

    // Initial bars
    g.selectAll(".bar")
      .data(data)
      .enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", (_d: any, i: number) => y(i.toString()) || 0)
      .attr("width", (d: any) => x(d.value))
      .attr("height", y.bandwidth())
      .attr("fill", (d: any) => d.color)
      .attr("rx", 4)
      .style("opacity", 0.9);

    // Initial labels
    g.selectAll(".label")
      .data(data)
      .enter()
      .append("text")
      .attr("class", "label")
      .attr("x", -10)
      .attr("y", (_d: any, i: number) => (y(i.toString()) || 0) + y.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("text-anchor", "end")
      .style("font-size", "14px")
      .style("font-weight", "bold")
      .style("fill", "#ffffff")
      .text((d: any) => d.name);

    // Initial values
    g.selectAll(".value")
      .data(data)
      .enter()
      .append("text")
      .attr("class", "value")
      .attr("x", (d: any) => x(d.value) + 10)
      .attr("y", (_d: any, i: number) => (y(i.toString()) || 0) + y.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("fill", "#ffffff")
      .text((d: any) => `$${(d.value / 1000).toFixed(0)}B`);

  }, [currentYear, generateYearData]); // Include dependencies

  // Animation update effect
  useEffect(() => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    const svgData = svg.datum() as {
      g: d3.Selection<SVGGElement, unknown, null, undefined>;
      xAxisGroup: d3.Selection<SVGGElement, unknown, null, undefined>;
      yearText: d3.Selection<SVGTextElement, unknown, null, undefined>;
      width: number;
      height: number;
      margin: { top: number; right: number; bottom: number; left: number };
    };

    if (!svgData) return;

    const { g, xAxisGroup, yearText, width, height } = svgData;

    const data = generateYearData(currentYear);
    const duration = isPlaying ? 250 : 500; // Faster transitions when playing

    // Update scales
    const maxValue = d3.max(data, d => d.value) || 0;
    const x = d3.scaleLinear()
      .domain([0, maxValue])
      .range([0, width]);

    const y = d3.scaleBand()
      .domain(data.map((_, i) => i.toString()))
      .range([0, height])
      .padding(0.1);

    // Update year display
    yearText
      .transition()
      .duration(duration)
      .text(currentYear);

    // Update x-axis
    xAxisGroup
      .transition()
      .duration(duration)
      .call(d3.axisBottom(x)
        .tickFormat(d => `$${(d as number / 1000).toFixed(0)}B`)
        .ticks(5))
      .selectAll("text")
      .style("fill", "#ffffff");

    // Data join for bars
    const bars = g.selectAll(".bar")
      .data(data, (d: any) => d.name);

    // Enter new bars
    const barsEnter = bars.enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", height) // Start from bottom
      .attr("width", 0)
      .attr("height", y.bandwidth())
      .attr("fill", (d: any) => d.color)
      .attr("rx", 4)
      .style("opacity", 0);

    // Update all bars (enter + existing)
    bars.merge(barsEnter)
      .transition()
      .duration(duration)
      .ease(d3.easeCubicInOut)
      .attr("y", (_d: any, i: number) => y(i.toString()) || 0)
      .attr("width", (d: any) => x(d.value))
      .attr("height", y.bandwidth())
      .style("opacity", 0.9);

    // Exit old bars
    bars.exit()
      .transition()
      .duration(duration)
      .attr("width", 0)
      .style("opacity", 0)
      .remove();

    // Data join for labels
    const labels = g.selectAll(".label")
      .data(data, (d: any) => d.name);

    // Enter new labels
    const labelsEnter = labels.enter()
      .append("text")
      .attr("class", "label")
      .attr("x", -10)
      .attr("y", height)
      .attr("dy", "0.35em")
      .style("text-anchor", "end")
      .style("font-size", "14px")
      .style("font-weight", "bold")
      .style("fill", "#ffffff")
      .style("opacity", 0)
      .text((d: any) => d.name);

    // Update all labels
    labels.merge(labelsEnter)
      .transition()
      .duration(duration)
      .ease(d3.easeCubicInOut)
      .attr("y", (_d: any, i: number) => (y(i.toString()) || 0) + y.bandwidth() / 2)
      .style("opacity", 1);

    // Exit old labels
    labels.exit()
      .transition()
      .duration(duration)
      .style("opacity", 0)
      .remove();

    // Data join for values
    const values = g.selectAll(".value")
      .data(data, (d: any) => d.name);

    // Enter new values
    const valuesEnter = values.enter()
      .append("text")
      .attr("class", "value")
      .attr("x", 10)
      .attr("y", height)
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("fill", "#ffffff")
      .style("opacity", 0)
      .text((d: any) => `$${(d.value / 1000).toFixed(0)}B`);

    // Update all values
    values.merge(valuesEnter)
      .transition()
      .duration(duration)
      .ease(d3.easeCubicInOut)
      .attr("x", (d: any) => x(d.value) + 10)
      .attr("y", (_d: any, i: number) => (y(i.toString()) || 0) + y.bandwidth() / 2)
      .style("opacity", 1)
      .tween("text", function(d: any) {
        const element = this as SVGTextElement;
        const interpolator = d3.interpolateNumber(
          +(element.textContent?.replace(/[$B,]/g, '') || '0'),
          d.value / 1000
        );
        return function(t: number) {
          element.textContent = `$${Math.round(interpolator(t))}B`;
        };
      });

    // Exit old values
    values.exit()
      .transition()
      .duration(duration)
      .style("opacity", 0)
      .remove();

  }, [currentYear, generateYearData, isPlaying]);

  const handlePlay = () => {
    if (isPlaying) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsPlaying(false);
    } else {
      setIsPlaying(true);
      intervalRef.current = setInterval(() => {
        setCurrentYear(prev => {
          if (prev >= 2025) {
            setIsPlaying(false);
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
            return 1900;
          }
          return prev + 2; // Smaller increments for smoother animation
        });
      }, 300); // Faster updates for smoother animation
    }
  };

  const handleYearChange = (year: number) => {
    setCurrentYear(year);
    if (isPlaying) {
      setIsPlaying(false);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <section className="section-padding bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Global Brands{' '}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Evolution
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Watch the rise and fall of the world&apos;s most valuable brands from 1900 to 2025
          </p>
        </div>

        {/* Chart Container */}
        <div className="bg-background/50 backdrop-blur-sm rounded-2xl border border-border p-8 w-auto">
          <div className="flex flex-col items-center">
            {/* SVG Chart */}
            <svg
              ref={svgRef}
              width="1000"
              height="500"
              className="mb-8"
            />

            {/* Controls */}
            <div className="flex flex-col items-center space-y-4 w-full max-w-2xl">
              {/* Play/Pause Button */}
              <button
                onClick={handlePlay}
                className="px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105"
              >
                {isPlaying ? 'Pause' : 'Play'} Animation
              </button>

              {/* Year Slider */}
              <div className="w-full">
                <input
                  type="range"
                  min="1900"
                  max="2025"
                  step="2"
                  value={currentYear}
                  onChange={(e) => handleYearChange(parseInt(e.target.value))}
                  className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-sm text-muted-foreground mt-2">
                  <span>1900</span>
                  <span className="font-bold text-primary">{currentYear}</span>
                  <span>2025</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #6366f1;
          cursor: pointer;
          box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #6366f1;
          cursor: pointer;
          border: none;
          box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
        }
      `}</style>
    </section>
  );
};

export default D3BarChart;
